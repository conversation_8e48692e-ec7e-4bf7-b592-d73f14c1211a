#include <TFile.h>
#include <TTree.h>
#include <TGraph2D.h>
#include <TCanvas.h>
#include <TStyle.h>
#include <TView.h>
#include <TAxis.h>
#include <TLegend.h>

#include <iostream>
#include <string>

// Função para remover espaços e quebras de linha
std::string trim(const std::string& s) {
    auto start = s.find_first_not_of(" \t\n\r");
    auto end = s.find_last_not_of(" \t\n\r");
    return (start == std::string::npos) ? "" : s.substr(start, end - start + 1);
}

void plot_heatmap_diftype() {
    TFile *file = TFile::Open("../data/sipmhits.root");
    if (!file || file->IsZombie()) {
        std::cerr << "Erro ao abrir o arquivo.\n";
        return;
    }

    TTree *tree = (TTree*)file->Get("SiPMHits");
    if (!tree) {
        std::cerr << "TTree 'SiPMHits' não encontrada.\n";
        return;
    }

    Double_t x, y, z;
    char tipo[20];  // buffer para string

    tree->SetBranchAddress("x", &x);
    tree->SetBranchAddress("y", &y);
    tree->SetBranchAddress("z", &z);
    tree->SetBranchAddress("type", &tipo);

    TGraph2D *graphVUV = new TGraph2D();
    TGraph2D *graphVIS = new TGraph2D();

    graphVUV->SetMarkerStyle(20); graphVUV->SetMarkerColor(kBlue);  // VUV em azul
    graphVIS->SetMarkerStyle(21); graphVIS->SetMarkerColor(kRed);   // VIS em vermelho

    Long64_t nEntries = tree->GetEntries();
    int iVUV = 0, iVIS = 0;

    for (Long64_t i = 0; i < nEntries; ++i) {
        tree->GetEntry(i);
        std::string tipoStr = trim(tipo);

        if (tipoStr == "VUV") {
            graphVUV->SetPoint(iVUV++, x, z, y);  // y para cima
        } else if (tipoStr == "VIS") {
            graphVIS->SetPoint(iVIS++, x, z, y);  // y para cima
        }
    }

    std::cout << "Total VUV hits: " << iVUV << "\n";
    std::cout << "Total VIS hits: " << iVIS << "\n";

    TCanvas *c = new TCanvas("c", "Photon Hits", 1000, 800);
    bool primeiro = true;

    if (iVUV > 0) {
        graphVUV->SetTitle("SiPM Hits por Tipo;x (cm);z (cm);y (cm)");
        graphVUV->Draw("P0");
        graphVUV->GetXaxis()->SetLimits(-5, 5);
        graphVUV->SetMinimum(-5);
        graphVUV->SetMaximum(5);
        primeiro = false;
    }

    if (iVIS > 0) {
        if (primeiro) {
            graphVIS->SetTitle("SiPM Hits por Tipo;x (cm);z (cm);y (cm)");
            graphVIS->Draw("P0");
            graphVIS->GetXaxis()->SetLimits(-5, 5);
            graphVIS->SetMinimum(-5);
            graphVIS->SetMaximum(5);
        } else {
            graphVIS->Draw("P0 SAME");
        }
    }

    // Legenda
    TLegend *legend = new TLegend(0.7, 0.8, 0.88, 0.9);
    if (iVUV > 0) legend->AddEntry(graphVUV, "VUV", "p");
    if (iVIS > 0) legend->AddEntry(graphVIS, "VIS", "p");
    legend->Draw();

    c->Modified();
    c->Update();
}
