#include <TFile.h>
#include <TTree.h>
#include <TGraph2D.h>
#include <TCanvas.h>
#include <TStyle.h>
#include <TView.h>
#include <TAxis.h>

void plot_heatmap() {
    TFile *file = TFile::Open("../data/sipmhits.root");
    TTree *tree = (TTree*)file->Get("SiPMHits");

    Double_t x, y, z;
    tree->SetBranchAddress("x", &x);
    tree->SetBranchAddress("y", &y);
    tree->SetBranchAddress("z", &z);

    Long64_t nEntries = tree->GetEntries();
    TGraph2D *graph = new TGraph2D();
    int idx = 0;
    for (Long64_t i = 0; i < nEntries; ++i) {
        tree->GetEntry(i);
        graph->SetPoint(idx++, x, z, y); // y para cima
    }

    TCanvas *c = new TCanvas("c", "Scatter 3D", 800, 600);
    graph->SetTitle("Photon Hits 3D;x (cm);z (cm);y (cm)");

    // Desenha primeiro para criar o histograma interno
    graph->Draw("P0");

    graph->GetXaxis()->SetLimits(-5, 5);
    //graph->GetYaxis()->SetLimits(-5, 5);
    graph->SetMinimum(-5);
    graph->SetMaximum(5);

    c->Modified();
    c->Update();
}
