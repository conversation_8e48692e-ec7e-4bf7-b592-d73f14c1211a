{"files.associations": {"iostream": "cpp", "ostream": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "buffer": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "netfwd": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "semaphore": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "variant": "cpp", "*.icc": "cpp", "aida_ntuple": "cpp", "ntuple_binding": "cpp", "ntuple_booking": "cpp", "raxml_out": "cpp", "rcsv_ntuple": "cpp", "base_leaf": "cpp", "branch": "cpp", "info": "cpp", "ntuple": "cpp", "tree": "cpp", "field_desc": "cpp", "wcsv_ntuple": "cpp", "bufobj": "cpp", "aidas": "cpp", "element": "cpp", "plotpoints.C": "cpp", "line": "cpp", "mesh": "cpp", "vec4": "cpp", "colorf": "cpp", "allocator": "cpp", "unordered_set": "cpp"}, "cmake.sourceDirectory": "/home/<USER>/geant4/codigos-testes/001"}