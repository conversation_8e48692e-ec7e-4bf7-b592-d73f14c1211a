
// Include the primary Generator
#include "src/MyPrimaryGenerator.h"

//Importing the files for the detector
#include "src/MyDetector.h" //puxa a parte do detector  para esse arquivo
#include "src/Materials.hh"

//For the Manager
#include "G4RunManager.hh"
#include "G4RunManager.hh"
#include "G4RunManagerFactory.hh"
#include "G4NeutrinoPhysics.hh"
// for The Physics List
#include "G4VUserPhysicsList.hh"
#include "G4PhysListFactory.hh"
#include "Shielding.hh"
#include "G4VModularPhysicsList.hh"
// Importing files for the action initialization
#include "src/MyActionInitialization.h"

// The Visualization interface
#include "G4VisExecutive.hh" //o openGl definido acima eh a variavel da palavra chave do G4VIS_USE
#include "G4VisManager.hh"
#include "G4OpticalPhysics.hh"

//The User Interface
#include "G4UIExecutive.hh"
#include "G4UImanager.hh"
#include "G4ios.hh"
#include "src/PhysicsList.h"

int main(int argc, char** argv){
    // importo o manager ja pronto
    //auto manager = G4RunManagerFactory :: CreateRunManager();
    G4RunManager* manager = new G4RunManager();

    
    //lets create the physics list using the factory and then destroy it
    auto factory  = new G4PhysListFactory();
    auto physicsList = factory->GetReferencePhysList("FTFP_BERT");
    auto opticalPhysics = new G4OpticalPhysics();

    auto opticalParameters = G4OpticalParameters::Instance();
    opticalParameters->SetWLSVerboseLevel(1);
    opticalParameters->SetWLSTimeProfile("exponential");
    opticalParameters->SetProcessActivation(G4OpticalProcessName(kScintillation), true);
    opticalParameters->SetScintByParticleType(true);    

    physicsList->RegisterPhysics(opticalPhysics);
    manager->SetUserInitialization(physicsList);


    manager->SetUserInitialization(new MyDetector());
    //manager->SetUserInitialization(new PhysicsList());
    manager->SetUserInitialization(new MyActionInitialization());

    // Inicializar o sistema de visualização
    auto visManager = new G4VisExecutive();
    //tell the manager to start the experiment!
    manager -> Initialize();
    G4cout << "=== kleber machado ===" << G4endl;
    visManager -> Initialise();
    Materials* materials = new Materials();
    materials->PrintNistMaterials();

    auto uiExecutive = new G4UIExecutive(argc, argv, "Qt");
    if (argc == 1){
        uiExecutive->SessionStart();
    }else{
        G4String fileName(argv[1]);
        auto uiManager = G4UImanager::GetUIpointer();
        uiManager->ApplyCommand("/control/execute  "+fileName);
        uiExecutive->SessionStart();
    
    };
    // numerof threads
    manager->SetNumberOfThreads(1);
    
    delete visManager;
    delete manager;
    delete uiExecutive; //ao terminar a sessao, delete-a
    return 0;
}
