#include "Materials.hh"
#include "G4NistManager.hh"
#include "G4SystemOfUnits.hh"
#include "G4MaterialPropertiesTable.hh"
#include "G4OpticalSurface.hh"
#include "G4NistManager.hh"
#include "G4LogicalBorderSurface.hh"
#include "G4Scintillation.hh"
// #include "Config.hh"
Materials::Materials() {};
Materials::~Materials(){};



void Materials::Construct() {
    auto nist = G4NistManager::Instance();
    ///////////////////////////////////////// Materiais gerais ////////////////////////////////////////////////////
    ssteel = nist->FindOrBuildMaterial("G4_STAINLESS-STEEL");

    const int N_DATA = 2;
    G4double photon_energies[N_DATA] = {1.0 * eV, 10.0 * eV};

    /////////////////////////////////////////////SILICON//////////////////////////////////////////////////
    Si = nist->FindOrBuildMaterial("G4_Si");
    auto siProps = new G4MaterialPropertiesTable(); // cria tabela de props
    G4double si_rindex[N_DATA] = {1.5, 1.5};
    G4double abslenght[N_DATA] = {1*nm, 1*nm};
    siProps->AddProperty("RINDEX", photon_energies, si_rindex, N_DATA);
    siProps->AddProperty("ABSLENGTH", photon_energies, abslenght, N_DATA);

    Si->SetMaterialPropertiesTable(siProps);

    //////////////////////////////////////////////FR4////////////////////////////////////////////////
    FR4 = new G4Material("FR4", 1.86 * g / cm3, 2);
    FR4->AddMaterial(nist->FindOrBuildMaterial("G4_Si"), 0.528);
    FR4->AddMaterial(nist->FindOrBuildMaterial("G4_C"), 0.472);
    auto FR4Props = new G4MaterialPropertiesTable(); // cria tabela de props
    G4double FR4_rindex[N_DATA] = {1.5, 1.5};
    FR4Props->AddProperty("RINDEX", photon_energies, FR4_rindex, N_DATA);

    FR4->SetMaterialPropertiesTable(FR4Props);

    ///////////////////////////////////////// VIKUITI ///////////////////////////////////////////////////
    Vikuiti = nist->FindOrBuildMaterial("G4_KAPTON");
    auto vikuitiProps = new G4MaterialPropertiesTable(); // cria tabela de props
    G4double vikuiti_rindex[N_DATA] = {1.5, 1.5};
    vikuitiProps->AddProperty("RINDEX", photon_energies, vikuiti_rindex, N_DATA);
    Vikuiti->SetMaterialPropertiesTable(vikuitiProps);

    /*
    G4double specularLobe[]    = {};
    G4double specularSpike[]   = {};
    G4double backscatter[]     = {};
    */

    /////////////////////////////////////////// LIQUID ARGON //////////////////////////////////////////
    lAr = nist->FindOrBuildMaterial("G4_lAr");
    auto lArProps = new G4MaterialPropertiesTable();
    const G4int n = 7;
    G4double energies[n] = {7.0*eV, 8.0*eV, 9.0*eV, 10.0*eV, 10.5*eV, 11.0*eV, 11.5*eV};
    G4double rindex[n] = {1.23,1.23,1.23,1.23,1.23,1.23,1.23};
    G4double fastComp[n] = {0., 0.1, 0.5, 1.0, 0.6, 0.2, 0.};
    G4double slowComp[n] = {0., 0.05, 0.3, 0.7, 0.5, 0.15, 0.};
    G4double absLength[n] = {100*cm, 100*cm, 100*cm, 100*cm, 100*cm, 100*cm, 100*cm}; // Absorption length

    lArProps->AddProperty("RINDEX", energies, rindex, n,true);
    lArProps->AddProperty("FASTCOMPONENT", energies, fastComp, n,true);
    lArProps->AddProperty("SLOWCOMPONENT", energies, slowComp, n,true);
    lArProps->AddProperty("SCINTILLATION", energies, fastComp, n,true);
    lArProps->AddProperty("ABSLENGTH", energies, absLength, n,true);

    lArProps->AddConstProperty("SCINTILLATIONYIELD", 36210. / MeV, true);
    lArProps->AddConstProperty("RESOLUTIONSCALE", 1.0);
    lArProps->AddConstProperty("FASTTIMECONSTANT", 6.*ns, true);
    lArProps->AddConstProperty("SLOWTIMECONSTANT", 1.3*us,true);
    lArProps->AddConstProperty("YIELDRATIO", 0.25,true);

    lAr->SetMaterialPropertiesTable(lArProps);

    lAr->GetIonisation()->SetBirksConstant(0.0000001*mm/MeV);

    ////////////////////////////////////////ACRILICO//////////////////////////////////////////////
    // Definindo os elementos necessários
    G4Element* elH = nist->FindOrBuildElement("H");
    G4Element* elC = nist->FindOrBuildElement("C");
    G4Element* elO = nist->FindOrBuildElement("O");

    // Criando o material acrílico (PMMA)
    Acrylic = new G4Material("Acrylic", 1.19 * g/cm3, 3);
    Acrylic->AddElement(elC, 5);
    Acrylic->AddElement(elH, 8);
    Acrylic->AddElement(elO, 2);
    auto acrylicProps = new G4MaterialPropertiesTable(); // cria tabela de props
    G4double acrylic_rindex[N_DATA] = {1.5, 1.5};
    acrylicProps->AddProperty("RINDEX", photon_energies, acrylic_rindex, N_DATA);

    Acrylic->SetMaterialPropertiesTable(acrylicProps);

   ////////////////////////////////////////////////////TEFLON//////////////////////////////////////
   Teflon = nist->FindOrBuildMaterial("G4_TEFLON");
   auto teflonProps = new G4MaterialPropertiesTable(); // cria tabela de props
   G4double teflon_rindex[N_DATA] = {1.35, 1.35};
   teflonProps->AddProperty("RINDEX", photon_energies, teflon_rindex, N_DATA);
   Teflon->SetMaterialPropertiesTable(teflonProps);
    
    //////////////////////////////////////// OPTICAL SURFACES ///////////////////////////////////////


    // lAr - Vikuiti
    lArVikSurface = new G4OpticalSurface("lArVSurface");
    lArVikSurface->SetModel(unified);
    lArVikSurface->SetType(dielectric_metal);
    lArVikSurface->SetFinish(polished);

    auto lArVikProps = new G4MaterialPropertiesTable();
    G4double lArVikReflectivity[N_DATA] = {0.97, 0.97};
    lArVikProps->AddProperty("REFLECTIVITY", photon_energies, lArVikReflectivity, N_DATA);
    lArVikSurface->SetMaterialPropertiesTable(lArVikProps);

    // Acrylic - lAr
    AcryliclArSurface = new G4OpticalSurface("AcryliclArSurface");
    AcryliclArSurface->SetModel(unified);
    AcryliclArSurface->SetType(dielectric_dielectric);
    AcryliclArSurface->SetFinish(polished);

    // Acrylic - PEN

    // PEN - lAr
    auto AcryliclArProps = new G4MaterialPropertiesTable();
    AcryliclArSurface->SetMaterialPropertiesTable(AcryliclArProps);

    // Si - lAr
    
    Si_lArSurface = new G4OpticalSurface("Si_lArSurface");
    Si_lArSurface->SetModel(unified);
    Si_lArSurface->SetType(dielectric_dielectric);
    Si_lArSurface->SetFinish(polished);

    auto Si_lArProps = new G4MaterialPropertiesTable();
    //G4double reflectivity[N_DATA] = {0.0, 0.0}; // sem reflexão
    auto sipmProps = new G4MaterialPropertiesTable();
    //sipmProps->AddProperty("REFLECTIVITY", photon_energies, reflectivity, N_DATA);
    Si_lArSurface->SetMaterialPropertiesTable(sipmProps);
}

#include "G4NistManager.hh"
#include "G4SystemOfUnits.hh"
#include "G4ios.hh"

void Materials::PrintNistMaterials() {
    G4cout << "==s= Lista de Materiais NIST disponíveis ===" << G4endl;
    G4NistManager* nist = G4NistManager::Instance();
    const std::vector<G4String>& matNames = nist->GetNistMaterialNames();

    G4cout << "=== Lista de Materiais NIST disponíveis ===" << G4endl;
    for (const auto& name : matNames) {
        G4cout << name << G4endl;
    }
}
