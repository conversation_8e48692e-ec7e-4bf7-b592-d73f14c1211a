import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D  # Necessário para projeção 3D

# Lista de pontos (x, y, z) em milímetros
points = [
    (26.5595, -28.873, -598.716),
    (-41.7307, 42.3171, -597.049),
    (0.998951, -13.3902, -599.85),
    (58.0052, -24.104, -596.703),
    (-10.6883, -37.5434, -598.729),
    (70.943, -30.0901, -595.031),
    (33.0439, -38.5593, -597.847),
    (8.3698, 56.2757, -597.296),
    (18.277, 72.6647, -595.303)
]

# Convertendo os pontos de mm para metros (multiplicando por 0.001)
points_in_meters = [(p[0] * 0.001, p[1] * 0.001, p[2] * 0.001) for p in points]

# Separar as coordenadas
x = [p[0] for p in points_in_meters]
y = [p[1] for p in points_in_meters]
z = [p[2] for p in points_in_meters]

# Criar figura e eixos 3D
fig = plt.figure()
ax = fig.add_subplot(111, projection='3d')

# Plotar os pontos como scatter
ax.scatter(x, y, z, color='red', s=50)

# Rótulos dos eixos e título
ax.set_xlabel('X (m)')
ax.set_ylabel('Y (m)')
ax.set_zlabel('Z (m)')
ax.set_title('Posições das Partículas (em metros)')

# Ajustar limites dos eixos para 1 metro (cada eixo)
ax.set_xlim([min(x)-0.5, max(x)+0.5])
ax.set_ylim([min(y)-0.5, max(y)+0.5])
ax.set_zlim([min(z)-0.5, max(z)+0.5])

# Exibir o gráfico
plt.show()

