#include "lArscint.hh"
#include "G4VUserDetectorConstruction.hh"
#include "G4Material.hh"
#include "G4NistManager.hh"
#include "G4Box.hh"
#include "G4SystemOfUnits.hh"
#include "G4LogicalVolume.hh"
#include "G4PVPlacement.hh"
#include "G4SubtractionSolid.hh"
#include "G4Tubs.hh"
#include "G4SDManager.hh"
#include "G4Sphere.hh"
#include "CLHEP/Units/SystemOfUnits.h"
#include "SensitiveDetector.hh"
#include <G4ios.hh>
#include "G4MultiUnion.hh"
#include "G4Color.hh"
#include "G4VisAttributes.hh"
#include "G4GDMLParser.hh"
#include "G4RotationMatrix.hh"
#include "Materials.hh"
#include <iostream>
#include "G4ios.hh"
#include "G4OpticalSurface.hh"
#include "G4LogicalSkinSurface.hh"
#include "G4LogicalBorderSurface.hh"
#include "G4Scintillation.hh"

using namespace CLHEP;
using namespace std;


G4VPhysicalVolume* MyDetector::Construct() {
    Materials *materials = new Materials();
    materials->Construct();
    auto lAr = materials->GetlAr();
    auto nist = G4NistManager::Instance();
    // --- Mundo ---
    G4double world_size = 1.0*m;
    auto solidWorld = new G4Box("World", world_size, world_size, world_size);
    auto logicWorld = new G4LogicalVolume(solidWorld, nist->FindOrBuildMaterial("G4_AIR"), "World");
    auto physWorld = new G4PVPlacement(0, {}, logicWorld, "World", nullptr, false, 0);

    // --- Cintilador: Argônio líquido ---
    G4double box_size = 10*cm;
    auto solidScint = new G4Box("Scintillator", box_size, box_size, box_size);

    auto logicScint = new G4LogicalVolume(solidScint, lAr, "Scintillator");
    new G4PVPlacement(0, {}, logicScint, "Scintillator", logicWorld, false, 0);

    //vis atributes
    auto visScint = new G4VisAttributes(G4Colour::Blue());
    auto visWorld = new G4VisAttributes(G4Colour::White());
    visWorld->SetForceWireframe(true);
    logicWorld->SetVisAttributes(visWorld);
    visScint->SetForceWireframe(true);
    logicScint->SetVisAttributes(visScint);

    return physWorld;
}

