#include "MyDetector.h"
#include "G4VUserDetectorConstruction.hh"
#include "G4Material.hh"
#include "G4NistManager.hh" //table of materials
#include "G4Box.hh"
#include "G4SystemOfUnits.hh"
#include "G4LogicalVolume.hh"
#include "G4PVPlacement.hh"
#include "G4SubtractionSolid.hh"
#include "G4Tubs.hh"
#include "G4SDManager.hh"
#include "G4Sphere.hh"
#include "CLHEP/Units/SystemOfUnits.h"
#include "G4Sphere.hh"
#include "SensitiveDetector.hh"
#include <G4ios.hh>
#include "G4MultiUnion.hh"
#include "G4Color.hh"
#include "G4VisAttributes.hh"

using namespace CLHEP;
G4ThreeVector origem(0,0,0);
G4VPhysicalVolume* MyDetector::Construct(){
//VAMOS DEFINIR OS MATERIAIS QUE VAMOS UTILIZAR
    auto lAr = G4NistManager::Instance()->FindOrBuildMaterial("G4_lAr");
    auto Ar = G4NistManager::Instance()->FindOrBuildMaterial("G4_Ar");
    auto air = G4NistManager::Instance()->FindOrBuildMaterial("G4_AIR");
    auto ssteel = G4NistManager::Instance()->FindOrBuildMaterial("G4_STAINLESS-STEEL");
    auto vacuo = G4NistManager::Instance()->FindOrBuildMaterial("G4_Galactic");
    auto carbon = G4NistManager::Instance()->FindOrBuildMaterial("G4_C");
    auto lead = G4NistManager::Instance()->FindOrBuildMaterial("G4_Pb");
    
//DEFININDO A GEOMETRIA DO MUNDO (UM CUBO OU UMA ESFERA)
    //auto worldBox = new G4Sphere("mundo",0,3*m,0,2*pi,0,2*pi);
    auto worldBox = new G4Box("mundo", 1*m,1*m,1*m);
    auto logicalWorld = new G4LogicalVolume(worldBox,Ar,"LogicalWorld");
    auto physicalWorld = new G4PVPlacement(0,{0,0,0},logicalWorld,"physicalWorld", 0, false, 0);//(rotation matrix,{x0,y0,z0}, what to place, name, mother volume, does it have more than one copy, what copy is it)

// DEFININDO OS TAMANHOS QUE VAMOS UTILIZAR PARA O CRIOSTATO

    // definindo as dimensões do cilindro externo do criostato
    G4double R = 20.0*cm;
    G4double R_int = 0*cm;
    G4double H = 1.42*m; //1,52
    
    // Definindo as dimensoes do cilindro que será subtraido do externo
    G4double r = 15*cm;
    G4double r_int = 0;
    G4double h = 1.75*m;

    // Para a calota que compoe o topo do criostato
    G4double raio_externo = 20 * cm; 
    G4double raio_interno =  0* cm;   
    G4double theta_min = 61.768* deg;     
    G4double theta_max = 28.231*deg; //37.326
    G4double phi_min = 0 * deg;     
    G4double phi_max = 360 * deg;  

// DEFININDO AS POSIÇÕES QUE VAMOS UTILIZAR PARA O CRIOSTATO 

    // definindo as posições das calotas de cima e de baixo
    G4ThreeVector posicao_topo(0,0,71*cm);
    G4ThreeVector posicao_baixo(0,0,-71*cm);
    //Definindo uma matriz de rotação de 180 graus com relação ao eixo y
    G4RotationMatrix* rotY180 = new G4RotationMatrix();
    rotY180->rotateY(180 * deg);  // Rotaciona 180 graus em torno de Z
    // Definindo a posição da geometria que contem o vacuo
    G4ThreeVector pos_vacuo(0,0,6.5*cm);
    
    // criando o cilindro externo do criostato
    auto CilindroExterno = new G4Tubs("Externo", R_int, R , H/2 , 0 * deg, 360 * deg);
    auto CilindroExterno_sub = new G4Tubs("Interno", r_int, r , h/2 , 0 * deg, 360 * deg);
    auto CilindroExternoSubtraido = new G4SubtractionSolid("CilindroSubtraido", CilindroExterno, CilindroExterno_sub);
    //Agora vamos fazer o cilindro de raio menor que encosta até o chao, vou chama-lo de interior(Criostato)
    G4double hh = 1.65*m;
    auto interior = new G4Tubs("Interno", 0, 15*cm , hh/2 , 0 * deg, 360 * deg);
    auto interior_sub = new G4Tubs("subInterno", 0 ,14.9*cm , hh/2 , 0 * deg, 360 * deg);
        
    auto interior_subtraido = new G4SubtractionSolid("interiorCriostato", 
        interior, 
        interior_sub, 
        nullptr, 
        G4ThreeVector(0.1*mm, 0, 0));  
        
        
    auto interior_2 = new G4Tubs("Interno2", 0, 14.9*cm , hh/2 , 0 * deg, 360 * deg);
    auto interior2_sub = new G4Tubs("subInterno2", 0 ,14.8*cm , hh/2 , 0 * deg, 360 * deg);
    auto interior2_subtraido = new G4SubtractionSolid("interiorCriostato2", 
                                interior_2, 
                                interior2_sub, 
                                nullptr, 
                                G4ThreeVector(0.1*mm, 0, 0));

    //auto log_interior = new G4LogicalVolume(interior_subtraido, ssteel, "interiorCriostato");
    //auto phys_interior = new G4PVPlacement(0,{0,0,-3.5*cm},log_interior,"interiorCriostato",logicalWorld,false,0);

// DEFININDO AS CALOTAS DA PARTE DE CIMA E DA PARTE DE BAIXO DO CRIOSTATO

    // Vamos definir agora a calota cortada por um cilindro que fz o formato arredondado do topo do criostato
    auto calota = new G4Sphere("CalotaEsferica",
                            raio_interno, raio_externo,
                            phi_min, phi_max,
                            theta_min, theta_max);


    auto calota1_subtraida = new G4SubtractionSolid("Topo", 
                            calota, 
                            interior_sub, 
                            nullptr, 
                            G4ThreeVector(0.1*mm, 0, 0));    

    // agora vamos fazer o abaulamento da parte de baixo, ele funciona do msm jeito da parte de cima, so que agora subtraindo o cilindrinho da parte de baixo
    auto calota_baixo = new G4Sphere("CalotaEsferica",
                            raio_interno, raio_externo,
                            phi_min, phi_max,
                            theta_min, theta_max);
    
    auto calota2_subtraida = new G4SubtractionSolid("Baixo", 
                            calota_baixo, 
                            interior, 
                            nullptr, 
                            G4ThreeVector(0.1*mm, 0, 0));
    // Juntando as coisas
    auto criostato_union = new G4MultiUnion("Criostato");
    criostato_union->AddNode(*CilindroExternoSubtraido, G4Transform3D(G4RotationMatrix(), origem));
    criostato_union->AddNode(*calota1_subtraida, G4Transform3D(G4RotationMatrix(), posicao_topo));
    criostato_union->AddNode(*calota2_subtraida, G4Transform3D(*rotY180, posicao_baixo));
    criostato_union->AddNode(*interior2_subtraido, G4Transform3D(G4RotationMatrix(), G4ThreeVector(0,0,-3.5*cm)));
    criostato_union->Voxelize();


    // criando os volumes logicos e posicionando no mundo
    auto log_criostato = new G4LogicalVolume(criostato_union, ssteel, "Criostato");
    auto vis_criostato = new G4VisAttributes(G4Colour(0.8, 0.8, 0.8, 1)); // (r,g,b,opcacidade)
    //mudar opacidade
    vis_criostato->SetForceSolid(true);
    log_criostato->SetVisAttributes(vis_criostato);

    auto phys_criostato = new G4PVPlacement(0,{0,0,0},log_criostato,"Criostato",logicalWorld,false,0);

// DEFFININDO O VACUO DENTRO DO CRIOSTATO
    /*
    auto vacuo_dentro = new G4Tubs("vacuo_dentro", 0, r , hh/2 , 0 * deg, 360 * deg);
    auto log_dentro = new G4LogicalVolume(vacuo_dentro, vacuo , "vacuo_dentro");
    auto phys_dentro = new G4PVPlacement(0,{0,0,-3.5*cm},log_dentro,"vacuo_dentro",logicalWorld,false,0);
*/
//---------------------------------------------------------------------------------------------------------------------------------------//

// DEFININDO AS POSIÇÕES QUE VAMOS UTILIZAR PARA A CONFECÇÃO DA CAPSULA
    
    G4ThreeVector deslocar_capsula(45*cm,0*cm,0);
    G4ThreeVector pos_cilindroInterno_capsula(0,0,-30*cm);
    G4ThreeVector pos_disco_1(0,0,38.5*cm - 30*cm);
    G4ThreeVector pos_disco_2(0,0,-38.5*cm-30*cm);
    G4ThreeVector pos_argonio_capsula(0,0,-30*cm);


    // DIMENSÕES DA CAPSULA INTERNA
    G4double raio_externo_capsula = 8.5 * cm;
    G4double raio_interno_capsula = 7.5 * cm;
    G4double altura_interno_capsula = 80 * cm;

    G4double raio_externo_disco = 10.5 * cm;
    G4double raio_interno_disco = 8.5 * cm;
    G4double altura_disco = 3 * cm;

    // POSIÇÕES RELATIVAS (em relação à origem da MultiUnion) pra capsula
    G4ThreeVector pos_cilindro(0, 0, -30 * cm);                       // centro do cilindro
    G4ThreeVector pos_disco1(0, 0, 38.5 * cm - 30 * cm);              // topo
    G4ThreeVector pos_disco2(0, 0, -38.5 * cm - 30 * cm);             // base

    // Criando cada solido da capsula
    auto tubo_externo = new G4Tubs("CorpoCapsula", 0, raio_externo_capsula, altura_interno_capsula/2, 0, 360*deg);
    auto tubo_interno = new G4Tubs("FuroCapsula", 0, raio_interno_capsula, altura_interno_capsula/2, 0, 360*deg);
    auto corpo_capsula = new G4SubtractionSolid("CorpoFurado", tubo_externo, tubo_interno);

    auto disco1 = new G4Tubs("Disco1", raio_interno_disco, raio_externo_disco, altura_disco/2, 0, 360*deg);
    auto disco2 = new G4Tubs("Disco2", raio_interno_disco, raio_externo_disco, altura_disco/2, 0, 360*deg);

    // Junta os solidos
    auto capsula_union = new G4MultiUnion("CapsulaCompleta");

    capsula_union->AddNode(*corpo_capsula, G4Transform3D(G4RotationMatrix(), pos_cilindro));
    capsula_union->AddNode(*disco1, G4Transform3D(G4RotationMatrix(), pos_disco1));
    capsula_union->AddNode(*disco2, G4Transform3D(G4RotationMatrix(), pos_disco2));
    capsula_union->Voxelize();

    // 4. Cria volume lógico e posiciona
    auto log_capsula = new G4LogicalVolume(capsula_union, ssteel, "Capsula");
    auto vis_capsula = new G4VisAttributes(G4Colour(0.8, 0.8, 0.8)); // cinza claro
    vis_capsula->SetForceSolid(true);
    log_capsula->SetVisAttributes(vis_capsula);
    auto phys_capsula = new G4PVPlacement(
        0,
        G4ThreeVector(45 * cm, 0, 0), // deslocar_capsula
        log_capsula,
        "Capsula",
        logicalWorld,
        false,
        0
    );


// FAZENDO O ARGONIO QUE FICA NO INTERIOR DA CAPSULA
    // Agora vamos definir o argonio gasoso que fica dentro da capsula
    auto argonio_dentro = new G4Tubs("argonio_dentro", 0, raio_externo_capsula, altura_interno_capsula/2,0 * deg, 360 * deg);  
    auto log_argonio_dentro = new G4LogicalVolume(argonio_dentro, lAr , "argonio_dentro");
    auto phys_argonio_dentro = new G4PVPlacement(0,pos_argonio_capsula + deslocar_capsula  ,log_argonio_dentro,"argonio_dentro",logicalWorld,false,0);

    auto detector =  new G4Tubs("detector", 0, raio_externo_capsula, altura_interno_capsula/2,0 * deg, 360 * deg);  
    log_sensor = new G4LogicalVolume(detector, lAr , "log_detector");
    //auto phys_sensor= new G4PVPlacement(0,pos_argonio_capsula + deslocar_capsula - deslocar_capsula ,log_sensor,"argonio_dentro",logicalWorld,false,0);
    auto phys_sensor= new G4PVPlacement(0,{0,0,0},log_sensor,"phys_detector",logicalWorld,false,0);
    return physicalWorld;
};

