#include "SensitiveDetector.hh"
#include "G4Step.hh"
#include "G4Track.hh"
#include "G4ParticleDefinition.hh"
#include "G4SystemOfUnits.hh"
#include <G4StepPoint.hh>
#include <G4String.hh>
#include <G4ios.hh>
#include <numeric>
#include "MyHit.h"
#include "G4AnalysisManager.hh"
#include "RunAction.hh"
#include "G4RunManager.hh"
using namespace CLHEP;
using namespace std;

// O construtor 
SensitiveDetector::SensitiveDetector(G4String name): G4VSensitiveDetector(name) {
    G4cout << "construtor" << G4endl;
}; 

SensitiveDetector::~SensitiveDetector(){
    G4cout << "destrutor" << G4endl;
};
/*
G4bool SensitiveDetector::ProcessHits(G4Step* aStep, G4TouchableHistory* ROhist){
// Detectar se energia é depositada no sipm
    if (aStep->GetTotalEnergyDeposit() != 0) {
        auto hit = new MyHit(aStep);
        G4String detectorName = this->GetName();
        if (detectorName.contains("VUV")) {
            hit->SetType("VUV");
        } else if (detectorName.contains("VIS")) {
            hit->SetType("VIS");
        }


        hit->Print();
        delete hit;
    }

    return true;
}
*/
G4bool SensitiveDetector::ProcessHits(G4Step* aStep, G4TouchableHistory* ROhist) {
    using namespace CLHEP;

    G4StepPoint* postStep = aStep->GetPreStepPoint();
    G4double energy = aStep->GetTrack()->GetKineticEnergy() / eV;

    // Tabelas de energia e eficiência para cada tipo de SiPM
    std::vector<double> energy_table = {2.0, 2.5, 3.0, 3.5, 4.0, 6.0, 7.0, 7.5, 8.0, 8.5, 9.0};
    std::vector<double> eff_VIS = {0.0, 0.3, 0.8, 0.3, 0.0, 0.0, 0.0, 0.5, 0.0, 0.0, 0.0};
    std::vector<double> eff_VUV = {0.0, 0.0, 0.0, 0.1, 0.0, 0.0, 0.1, 0.5, 0.7, 0.4, 0.1};

    // Função de interpolação linear
    auto interpolate = [](double x, const std::vector<double>& xv, const std::vector<double>& yv) {
        if (x <= xv.front()) return yv.front();
        if (x >= xv.back()) return yv.back();
        for (size_t i = 0; i < xv.size() - 1; ++i) {
            if (x >= xv[i] && x < xv[i + 1]) {
                double t = (x - xv[i]) / (xv[i + 1] - xv[i]);
                return yv[i] + t * (yv[i + 1] - yv[i]);
            }
        }
        return 0.0;
    };

    // Define eficiência com base no tipo do detector
    G4String detectorName = this->GetName();
    G4double efficiency = 0.0;

    if (G4StrUtil::contains(detectorName, "VUV")) {
        efficiency = interpolate(energy, energy_table, eff_VUV);
    } else if (G4StrUtil::contains(detectorName, "VIS")) {
        efficiency = interpolate(energy, energy_table, eff_VIS);
    }

    // Gera hit com base na eficiência
    if (G4UniformRand() < efficiency) {
        auto hit = new MyHit(aStep);
        if (G4StrUtil::contains(detectorName, "VUV")) {
            hit->SetType("VUV");
        } else if (G4StrUtil::contains(detectorName, "VIS")) {
            hit->SetType("VIS");
        }

        hit->Print();
        delete hit;
    }

    return true;
}

