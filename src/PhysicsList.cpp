#include "PhysicsList.h"
#include "G4VUserPhysicsList.hh"
#include "G4PhysListFactory.hh"
#include "G4EmStandardPhysics.hh"
#include "G4EmExtraPhysics.hh"
#include "G4IonPhysics.hh"
#include "G4DecayPhysics.hh"
#include "G4Scintillation.hh"
#include "G4ProcessManager.hh"
#include "G4ParticleDefinition.hh"

PhysicsList::PhysicsList(u_int8_t verbose)
{
    SetVerboseLevel(verbose);

    // Optical Physics
    auto opticalPhysics = new G4OpticalPhysics(verbose);    
    auto opticalParameters = G4OpticalParameters::Instance();

    opticalParameters->SetWLSVerboseLevel(verbose);
    opticalParameters->SetWLSTimeProfile("exponential");

    opticalParameters->SetProcessActivation(G4OpticalProcessName(kScintillation), true);
    opticalParameters->SetScintByParticleType(true); // ativa cintilação por tipo
    opticalParameters->SetScintTrackInfo(true);

    RegisterPhysics(opticalPhysics);

    auto fastSimulationPhysics = new G4FastSimulationPhysics();
    fastSimulationPhysics->ActivateFastSimulation("opticalphoton");
    RegisterPhysics(fastSimulationPhysics);

    RegisterPhysics(new G4EmStandardPhysics(verbose));
    RegisterPhysics(new G4IonPhysics());  // Enable ion physics for alpha particles
    RegisterPhysics(new G4DecayPhysics(verbose));

}

void PhysicsList::ConstructProcess() {
    G4VModularPhysicsList::ConstructProcess();

    // The scintillation process should be handled by G4OpticalPhysics
    // Let's check if the issue is with the process configuration
    G4cout << "\n=== Checking Scintillation Process Configuration ===" << G4endl;

    auto particleIterator = GetParticleIterator();
    particleIterator->reset();

    while ((*particleIterator)()) {
        G4ParticleDefinition* particle = particleIterator->value();
        if (particle->GetParticleName() == "alpha") {
            G4ProcessManager* pmanager = particle->GetProcessManager();
            G4ProcessVector* processVector = pmanager->GetProcessList();

            G4cout << "Processes for alpha particle:" << G4endl;
            for (size_t i = 0; i < processVector->size(); i++) {
                G4VProcess* process = (*processVector)[i];
                G4cout << "  " << i << ": " << process->GetProcessName() << G4endl;
            }

            G4VProcess* scintProcess = pmanager->GetProcess("Scintillation");
            if (scintProcess) {
                G4cout << "Scintillation process found for alpha!" << G4endl;
                G4cout << "Process type: " << scintProcess->GetProcessType() << G4endl;
                G4cout << "Process sub-type: " << scintProcess->GetProcessSubType() << G4endl;
            } else {
                G4cout << "NO scintillation process found for alpha!" << G4endl;
            }
            break;
        }
    }
    G4cout << "================================================\n" << G4endl;
}
