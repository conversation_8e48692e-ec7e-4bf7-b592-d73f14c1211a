#include "PhysicsList.h"
#include "G4VUserPhysicsList.hh"
#include "G4PhysListFactory.hh"
#include "G4EmStandardPhysics.hh"
#include "G4EmExtraPhysics.hh"
#include "G4IonPhysics.hh"
#include "G4DecayPhysics.hh"
#include "G4Scintillation.hh"

PhysicsList::PhysicsList(u_int8_t verbose)
{
    SetVerboseLevel(verbose);

    // Optical Physics
    auto opticalPhysics = new G4OpticalPhysics(verbose);    
    auto opticalParameters = G4OpticalParameters::Instance();

    opticalParameters->SetWLSVerboseLevel(verbose);
    opticalParameters->SetWLSTimeProfile("exponential");

    opticalParameters->SetProcessActivation(G4OpticalProcessName(kScintillation), true);
    opticalParameters->SetScintByParticleType(true); // ativa cintilação por tipo
    opticalParameters->SetScintTrackInfo(true);

    RegisterPhysics(opticalPhysics);

    auto fastSimulationPhysics = new G4FastSimulationPhysics();
    fastSimulationPhysics->ActivateFastSimulation("opticalphoton");
    RegisterPhysics(fastSimulationPhysics);

    RegisterPhysics(new G4EmStandardPhysics(verbose));
    RegisterPhysics(new G4IonPhysics());  // Enable ion physics for alpha particles
    RegisterPhysics(new G4DecayPhysics(verbose));

}

void PhysicsList::ConstructProcess() {
    G4VModularPhysicsList::ConstructProcess();
    // Scintillation process is already handled by G4OpticalPhysics
    // No need to add it manually here
}
