#include "PhysicsList.h"
#include "G4VUserPhysicsList.hh"
#include "G4PhysListFactory.hh"
#include "G4EmStandardPhysics.hh"
#include "G4EmExtraPhysics.hh"
#include "G4IonPhysics.hh"
#include "G4DecayPhysics.hh"
#include "G4Scintillation.hh"
#include "G4ProcessManager.hh"
#include "G4ParticleDefinition.hh"

PhysicsList::PhysicsList(u_int8_t verbose)
{
    SetVerboseLevel(verbose);

    // Optical Physics
    auto opticalPhysics = new G4OpticalPhysics(verbose);    
    auto opticalParameters = G4OpticalParameters::Instance();

    opticalParameters->SetWLSVerboseLevel(verbose);
    opticalParameters->SetWLSTimeProfile("exponential");

    opticalParameters->SetProcessActivation(G4OpticalProcessName(kScintillation), true);
    opticalParameters->SetScintByParticleType(true); // ativa cintilação por tipo
    opticalParameters->SetScintTrackInfo(true);

    RegisterPhysics(opticalPhysics);

    auto fastSimulationPhysics = new G4FastSimulationPhysics();
    fastSimulationPhysics->ActivateFastSimulation("opticalphoton");
    RegisterPhysics(fastSimulationPhysics);

    RegisterPhysics(new G4EmStandardPhysics(verbose));
    RegisterPhysics(new G4IonPhysics());  // Enable ion physics for alpha particles
    RegisterPhysics(new G4DecayPhysics(verbose));

}

void PhysicsList::ConstructProcess() {
    G4VModularPhysicsList::ConstructProcess();

    // Try a completely different approach - manually add scintillation process
    G4cout << "\n=== Manual Scintillation Configuration ===" << G4endl;

    // Create a new scintillation process with specific settings
    auto scintillation = new G4Scintillation("Scintillation");
    scintillation->SetScintillationByParticleType(false);  // Disable particle-specific scintillation
    scintillation->SetTrackSecondariesFirst(true);
    scintillation->SetVerboseLevel(2);

    auto particleIterator = GetParticleIterator();
    particleIterator->reset();

    while ((*particleIterator)()) {
        G4ParticleDefinition* particle = particleIterator->value();
        G4ProcessManager* pmanager = particle->GetProcessManager();

        if (particle->GetParticleName() == "alpha") {
            G4cout << "Manually adding scintillation to alpha particles" << G4endl;

            // Remove existing scintillation process
            G4VProcess* existingScint = pmanager->GetProcess("Scintillation");
            if (existingScint) {
                pmanager->RemoveProcess(existingScint);
                G4cout << "Removed existing scintillation process" << G4endl;
            }

            // Add our custom scintillation process
            pmanager->AddProcess(scintillation);
            pmanager->SetProcessOrderingToLast(scintillation, idxAtRest);
            pmanager->SetProcessOrderingToLast(scintillation, idxPostStep);

            G4cout << "Added custom scintillation process to alpha" << G4endl;
            break;
        }
    }
    G4cout << "===========================================\n" << G4endl;
}
