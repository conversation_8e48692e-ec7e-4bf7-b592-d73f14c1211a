#include "PhysicsList.h"
#include "G4VUserPhysicsList.hh"
#include "G4PhysListFactory.hh"
#include "G4EmStandardPhysics.hh"
#include "G4EmExtraPhysics.hh"
#include "G4IonPhysics.hh"
#include "G4DecayPhysics.hh"
#include "G4Scintillation.hh"
#include "G4ProcessManager.hh"
#include "G4ParticleDefinition.hh"

PhysicsList::PhysicsList(u_int8_t verbose)
{
    SetVerboseLevel(verbose);

    // Optical Physics
    auto opticalPhysics = new G4OpticalPhysics(verbose);    
    auto opticalParameters = G4OpticalParameters::Instance();

    opticalParameters->SetWLSVerboseLevel(verbose);
    opticalParameters->SetWLSTimeProfile("exponential");

    opticalParameters->SetProcessActivation(G4OpticalProcessName(kScintillation), true);
    opticalParameters->SetScintByParticleType(true); // ativa cintilação por tipo
    opticalParameters->SetScintTrackInfo(true);

    RegisterPhysics(opticalPhysics);

    auto fastSimulationPhysics = new G4FastSimulationPhysics();
    fastSimulationPhysics->ActivateFastSimulation("opticalphoton");
    RegisterPhysics(fastSimulationPhysics);

    RegisterPhysics(new G4EmStandardPhysics(verbose));
    RegisterPhysics(new G4IonPhysics());  // Enable ion physics for alpha particles
    RegisterPhysics(new G4DecayPhysics(verbose));

}

void PhysicsList::ConstructProcess() {
    G4VModularPhysicsList::ConstructProcess();

    // FINAL FIX: Force scintillation to work by ensuring it's called at the right time
    G4cout << "\n=== FINAL SCINTILLATION FIX ===" << G4endl;

    // Get the scintillation process from G4OpticalPhysics and configure it properly
    auto particleIterator = GetParticleIterator();
    particleIterator->reset();

    while ((*particleIterator)()) {
        G4ParticleDefinition* particle = particleIterator->value();

        // Focus only on alpha particles for now
        if (particle->GetParticleName() == "alpha") {
            G4ProcessManager* pmanager = particle->GetProcessManager();
            G4VProcess* scintProcess = pmanager->GetProcess("Scintillation");

            if (scintProcess) {
                G4cout << "Configuring scintillation for alpha particles" << G4endl;

                // Cast to G4Scintillation to access specific methods
                G4Scintillation* scint = dynamic_cast<G4Scintillation*>(scintProcess);
                if (scint) {
                    // Configure the scintillation process properly
                    scint->SetScintillationByParticleType(false);
                    scint->SetTrackSecondariesFirst(true);
                    scint->SetVerboseLevel(2);

                    // Try to force scintillation to work by setting finite step limit
                    scint->SetFiniteRiseTime(false);

                    G4cout << "Scintillation process configured with:" << G4endl;
                    G4cout << "  - Scintillation by particle type: " << scint->GetScintillationByParticleType() << G4endl;
                    G4cout << "  - Track secondaries first: " << scint->GetTrackSecondariesFirst() << G4endl;
                    G4cout << "  - Finite rise time: " << scint->GetFiniteRiseTime() << G4endl;

                    G4cout << "Scintillation configured for alpha particles" << G4endl;
                    G4cout << "Scintillation by particle type: " << scint->GetScintillationByParticleType() << G4endl;
                    G4cout << "Track secondaries first: " << scint->GetTrackSecondariesFirst() << G4endl;
                }
            } else {
                G4cout << "ERROR: No scintillation process found for alpha!" << G4endl;
            }
            break;
        }
    }
    G4cout << "==============================\n" << G4endl;
}
