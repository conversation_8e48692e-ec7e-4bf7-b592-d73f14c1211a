#pragma once
#include "G4VHit.hh"
#include "G4THitsCollection.hh"
#include "G4Allocator.hh"
#include "G4ThreeVector.hh"
#include "G4Step.hh"
#include "G4SystemOfUnits.hh"

class MyHit : public G4VHit {
public:
    MyHit(G4Step* aStep);
    ~MyHit();
    void Print() override;
    //void Draw() override;

    // Getters
    inline G4int GetTrackID() const { return fTrackID; }
    inline G4ThreeVector GetPosition() const { return fPosition; }
    inline G4String GetParticleName() const { return fParticleName; }
    inline G4double GetEnergyDeposit() const { return fEnergyDeposit; }
    void SetType(G4String type) { fType = type; }
private:
    G4ThreeVector fPosition;
    G4int fTrackID;
    G4String fParticleName;
    G4double fEnergyDeposit;
    G4Step* aStep;
    G4String fType;
};


