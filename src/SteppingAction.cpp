#include "SteppingAction.h"
#include "G4StepPoint.hh"
#include "G4VPhysicalVolume.hh"
#include "G4Material.hh"
#include "G4ProcessManager.hh"
#include "G4ProcessVector.hh"
#include "G4Scintillation.hh"
#include "G4VParticleChange.hh"
#include "G4OpticalPhoton.hh"
#include "G4PhysicsOrderedFreeVector.hh"
#include "G4MaterialPropertiesTable.hh"
#include "Randomize.hh"
#include "G4DynamicParticle.hh"
#include "G4TrackVector.hh"
#include "CLHEP/Random/RandPoisson.h"

SteppingAction::SteppingAction()
    : G4UserSteppingAction(),
      fOpticalPhotonCount(0),
      fTotalEnergyDeposited(0.0),
      fFirstOpticalPhoton(true)
{
}

SteppingAction::~SteppingAction()
{
}

void SteppingAction::UserSteppingAction(const G4Step* step)
{
    G4Track* track = step->GetTrack();
    G4ParticleDefinition* particle = track->GetDefinition();
    
    // Count optical photons
    if (particle == G4OpticalPhoton::OpticalPhotonDefinition()) {
        fOpticalPhotonCount++;
        
        if (fFirstOpticalPhoton) {
            G4cout << "\n=== FIRST OPTICAL PHOTON DETECTED! ===" << G4endl;
            G4cout << "Track ID: " << track->GetTrackID() << G4endl;
            G4cout << "Parent ID: " << track->GetParentID() << G4endl;
            G4cout << "Energy: " << G4BestUnit(track->GetKineticEnergy(), "Energy") << G4endl;
            G4cout << "Position: " << G4BestUnit(track->GetPosition(), "Length") << G4endl;
            G4cout << "Creator Process: " << track->GetCreatorProcess()->GetProcessName() << G4endl;
            G4cout << "====================================\n" << G4endl;
            fFirstOpticalPhoton = false;
        }
        
        // Print every 1000th optical photon
        if (fOpticalPhotonCount % 1000 == 0) {
            G4cout << "Optical photon count: " << fOpticalPhotonCount << G4endl;
        }
    }
    
    // Track energy deposition in LAr
    G4Material* material = step->GetPreStepPoint()->GetMaterial();
    if (material && material->GetName() == "LAr_Custom") {
        G4double edep = step->GetTotalEnergyDeposit();
        if (edep > 0.0) {
            fTotalEnergyDeposited += edep;
            
            // Print detailed information for energy deposition steps
            if (particle->GetParticleName() == "alpha") {
                G4cout << "Alpha energy deposition: " << G4BestUnit(edep, "Energy")
                       << " at " << G4BestUnit(track->GetPosition(), "Length") << G4endl;

                // MANUAL SCINTILLATION TRIGGER FOR ALPHA PARTICLES
                // Since the automatic scintillation process isn't working, let's manually create optical photons
                ManuallyTriggerScintillation(step, edep);
            }
        }
    }
}

void SteppingAction::Reset()
{
    fOpticalPhotonCount = 0;
    fTotalEnergyDeposited = 0.0;
    fFirstOpticalPhoton = true;
}

void SteppingAction::ManuallyTriggerScintillation(const G4Step* step, G4double energyDeposit)
{
    // Get material properties
    G4Material* material = step->GetPreStepPoint()->GetMaterial();
    G4MaterialPropertiesTable* mpt = material->GetMaterialPropertiesTable();

    if (!mpt) {
        G4cout << "ERROR: No material properties table for manual scintillation!" << G4endl;
        return;
    }

    // Get scintillation yield (photons/MeV)
    G4double scintillationYield = 0.0;
    if (mpt->ConstPropertyExists("SCINTILLATIONYIELD")) {
        scintillationYield = mpt->GetConstProperty("SCINTILLATIONYIELD");
    } else {
        G4cout << "ERROR: No SCINTILLATIONYIELD property!" << G4endl;
        return;
    }

    // Calculate number of optical photons to create
    G4double meanNumberOfPhotons = scintillationYield * energyDeposit / MeV;
    G4int numberOfPhotons = CLHEP::RandPoisson::shoot(meanNumberOfPhotons);

    if (numberOfPhotons > 0) {
        G4cout << "MANUAL SCINTILLATION: Creating " << numberOfPhotons
               << " optical photons from " << G4BestUnit(energyDeposit, "Energy")
               << " energy deposit" << G4endl;

        // Get the track and step information
        G4Track* track = step->GetTrack();
        G4StepPoint* postStepPoint = step->GetPostStepPoint();
        G4ThreeVector position = postStepPoint->GetPosition();
        G4double time = postStepPoint->GetGlobalTime();

        // Create optical photons
        for (G4int i = 0; i < numberOfPhotons; i++) {
            // Create optical photon with 128 nm wavelength (typical for LAr)
            G4double photonEnergy = 9.7 * eV; // 128 nm

            // Random direction
            G4double cosTheta = 2.0 * G4UniformRand() - 1.0;
            G4double sinTheta = std::sqrt(1.0 - cosTheta * cosTheta);
            G4double phi = 2.0 * CLHEP::pi * G4UniformRand();
            G4ThreeVector photonDirection(sinTheta * std::cos(phi),
                                        sinTheta * std::sin(phi),
                                        cosTheta);

            // Create the optical photon track
            G4DynamicParticle* photon = new G4DynamicParticle(G4OpticalPhoton::OpticalPhoton(),
                                                            photonDirection,
                                                            photonEnergy);

            G4Track* photonTrack = new G4Track(photon, time, position);
            photonTrack->SetParentID(track->GetTrackID());

            // For now, just count the photons we would create
            // (We can't easily add secondaries from stepping action)
            fOpticalPhotonCount++;

            // Clean up the track since we're not actually using it yet
            delete photonTrack;
        }
    }
}
