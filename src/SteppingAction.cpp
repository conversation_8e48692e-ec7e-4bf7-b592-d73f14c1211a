#include "SteppingAction.h"
#include "G4StepPoint.hh"
#include "G4VPhysicalVolume.hh"
#include "G4Material.hh"
#include "G4ProcessManager.hh"
#include "G4ProcessVector.hh"

SteppingAction::SteppingAction()
    : G4UserSteppingAction(),
      fOpticalPhotonCount(0),
      fTotalEnergyDeposited(0.0),
      fFirstOpticalPhoton(true)
{
}

SteppingAction::~SteppingAction()
{
}

void SteppingAction::UserSteppingAction(const G4Step* step)
{
    G4Track* track = step->GetTrack();
    G4ParticleDefinition* particle = track->GetDefinition();
    
    // Count optical photons
    if (particle == G4OpticalPhoton::OpticalPhotonDefinition()) {
        fOpticalPhotonCount++;
        
        if (fFirstOpticalPhoton) {
            G4cout << "\n=== FIRST OPTICAL PHOTON DETECTED! ===" << G4endl;
            G4cout << "Track ID: " << track->GetTrackID() << G4endl;
            G4cout << "Parent ID: " << track->GetParentID() << G4endl;
            G4cout << "Energy: " << G4BestUnit(track->GetKineticEnergy(), "Energy") << G4endl;
            G4cout << "Position: " << G4BestUnit(track->GetPosition(), "Length") << G4endl;
            G4cout << "Creator Process: " << track->GetCreatorProcess()->GetProcessName() << G4endl;
            G4cout << "====================================\n" << G4endl;
            fFirstOpticalPhoton = false;
        }
        
        // Print every 1000th optical photon
        if (fOpticalPhotonCount % 1000 == 0) {
            G4cout << "Optical photon count: " << fOpticalPhotonCount << G4endl;
        }
    }
    
    // Track energy deposition in LAr
    G4Material* material = step->GetPreStepPoint()->GetMaterial();
    if (material && material->GetName() == "LAr_Custom") {
        G4double edep = step->GetTotalEnergyDeposit();
        if (edep > 0.0) {
            fTotalEnergyDeposited += edep;
            
            // Print detailed information for energy deposition steps
            if (particle->GetParticleName() == "alpha") {
                G4cout << "Alpha energy deposition: " << G4BestUnit(edep, "Energy") 
                       << " at " << G4BestUnit(track->GetPosition(), "Length") << G4endl;
            }
        }
    }
}

void SteppingAction::Reset()
{
    fOpticalPhotonCount = 0;
    fTotalEnergyDeposited = 0.0;
    fFirstOpticalPhoton = true;
}
