#pragma once

//For The Detector
#include "G4VUserDetectorConstruction.hh"
#include "G4Material.hh"
#include "G4NistManager.hh" //table of materials
#include "G4Box.hh"
#include "G4LogicalVolume.hh"
#include "G4PVPlacement.hh"
#include "G4SDManager.hh"
#include "SensitiveDetector.hh"
class MyDetector : public G4VUserDetectorConstruction {
public:
    MyDetector() = default;
    ~MyDetector() = default;
    G4VPhysicalVolume* Construct() override;
    void ConstructSDandField() override;
private:
    G4LogicalVolume* log_sensor; 
    G4LogicalVolume* logicSiPMs;
    std::vector<G4LogicalVolume*> logicSiPMsVector;
};
