#include "RunAction.hh"
#include "G4AnalysisManager.hh"
#include "G4Run.hh"

RunAction::RunAction() {}
RunAction::~RunAction() {}

void RunAction::BeginOfRunAction(const G4Run*) {
    auto* man = G4AnalysisManager::Instance();
    man->OpenFile("../data/sipmhits.root");

    man->CreateNtuple("SiPMHits", "Photon detection positions");
    man->CreateNtupleDColumn("x");
    man->CreateNtupleDColumn("y");
    man->CreateNtupleDColumn("z");
    man->CreateNtupleSColumn("type"); // tipo VUV ou VIS
    man->CreateNtupleDColumn("energy");
    man->FinishNtuple();
}

void RunAction::EndOfRunAction(const G4Run*) {
    auto* man = G4AnalysisManager::Instance();
    man->Write();
    man->CloseFile();
}
