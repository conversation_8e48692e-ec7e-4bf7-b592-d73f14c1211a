#pragma once
#include "G4UserRunAction.hh"
#include "G4AnalysisManager.hh"
#include "G4ThreeVector.hh"
#include "TFile.h"
#include "TTree.h"


class RunAction : public G4UserRunAction {
public:
    RunAction();
    ~RunAction();
    virtual void BeginOfRunAction(const G4Run* run) override;
    virtual void EndOfRunAction(const G4Run* run) override;

    void FillHit(G4ThreeVector pos);

private:
    G4AnalysisManager *man;
    TFile *file;
    TTree *tree;
    Double_t x, y, z;
};