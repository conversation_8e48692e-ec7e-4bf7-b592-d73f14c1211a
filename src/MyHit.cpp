#include "MyHit.h"
#include "G4SystemOfUnits.hh"
#include "CLHEP/Units/SystemOfUnits.h"
#include "G4AnalysisManager.hh"
#include "G4VSensitiveDetector.hh"

using namespace CLHEP;

MyHit::MyHit(G4Step* aStep) {
    fPosition = aStep->GetPreStepPoint()->GetPosition();
    fTrackID = aStep->GetTrack()->GetTrackID();
    fParticleName = aStep->GetTrack()->GetDefinition()->GetParticleName();
    fEnergyDeposit = aStep->GetTotalEnergyDeposit();

    // Determinar o tipo do SiPM com base no nome do sensitive detector
    auto sdName = aStep->GetPreStepPoint()->GetSensitiveDetector()->GetName();
    if (sdName.contains("VUV")) {
        fType = "VUV";
    } else if (sdName.contains("VIS")) {
        fType = "VIS";
    } else {
        fType = "Unknown";
    }
}   

MyHit::~MyHit() {}

void MyHit::Print() {
/*
    G4cout << 
        "------------------------------------" <<
        G4endl << "Posição: " << fPosition / cm << " cm" <<
        G4endl << "Track ID: " << fTrackID <<
        G4endl << "Particle Name: " << fParticleName <<
        G4endl << "Energy Deposit: " << fEnergyDeposit / eV << " eV" <<
        G4endl << "------------------------------------" <<
        G4endl; 
*/

    // Grava no ntuple
    auto* man = G4AnalysisManager::Instance();
    man->FillNtupleDColumn(0, fPosition.x() / cm);
    man->FillNtupleDColumn(1, fPosition.y() / cm);
    man->FillNtupleDColumn(2, fPosition.z() / cm);
    man->FillNtupleSColumn(3, fType);  // tipo VUV ou VIS
    man->FillNtupleDColumn(4,fEnergyDeposit/eV);
    man->AddNtupleRow();

}
