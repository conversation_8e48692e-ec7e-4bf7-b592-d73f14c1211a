#pragma once

#include "G4VUserPrimaryGeneratorAction.hh"
#include "G4Electron.hh"
#include "G4ParticleGun.hh"
#include "G4Event.hh"

// Let's create the particle generator
class MyPrimaryGenerator: public G4VUserPrimaryGeneratorAction{
public:

    MyPrimaryGenerator();
    ~MyPrimaryGenerator();
    
    virtual void GeneratePrimaries(G4Event* anEvent) override; //anEvent is a particle generation event -> many particles
    G4ParticleGun* GetParticleGun() const { return mParticleGun; }
private:
    G4ParticleGun *mParticleGun; 
};

