#include "MyPrimaryGenerator.h"
#include "G4ParticleTable.hh"
#include "G4ParticleDefinition.hh"
#include "G4Event.hh"
#include "G4SystemOfUnits.hh"
#include "G4GeneralParticleSource.hh"
#include "G4RandomDirection.hh"

MyPrimaryGenerator::MyPrimaryGenerator() {

    mParticleGun = new G4ParticleGun(2); 
    auto particleTable = G4ParticleTable::GetParticleTable();

/*
    auto particle = particleTable->FindParticle("opticalphoton"); 
    mParticleGun->SetParticleDefinition(particle);
    mParticleGun->SetParticleEnergy(5.0 * eV);
    G4ThreeVector direction(1, 0, 0);  // direção +z
    mParticleGun->SetParticleMomentumDirection(direction);
    mParticleGun->SetParticlePosition(G4ThreeVector(0, 0, 0* cm)); // próximo do fundo da caixa

    // Definindo a polarização: um vetor ortogonal à direção do fóton
    // Para direção (0,0,1), por exemplo, polarização pode ser (1,0,0)
    //G4ThreeVector polarization = G4ThreeVector(1, 0, 0);
    //mParticleGun->SetParticlePolarization(polarization.unit());
*/

    mParticleGun->SetParticleMomentumDirection(G4RandomDirection());
}

MyPrimaryGenerator::~MyPrimaryGenerator() {
    delete mParticleGun;
}

void MyPrimaryGenerator::GeneratePrimaries(G4Event* event) {
    // gerar um vertice primario para cada iteração do loop
    G4int nVertices = 10; // quantos vc quer por evento
    for(int i = 0; i < nVertices; ++i){
        mParticleGun->GeneratePrimaryVertex(event);

    }
}
