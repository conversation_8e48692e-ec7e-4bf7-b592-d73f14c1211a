#ifndef STEPPINGACTION_H
#define STEPPINGACTION_H

#include "G4UserSteppingAction.hh"
#include "G4Step.hh"
#include "G4Track.hh"
#include "G4OpticalPhoton.hh"
#include "G4SystemOfUnits.hh"
#include "G4UnitsTable.hh"
#include "globals.hh"

class SteppingAction : public G4UserSteppingAction
{
public:
    SteppingAction();
    virtual ~SteppingAction();
    
    virtual void UserSteppingAction(const G4Step* step) override;
    
    void Reset();
    G4int GetOpticalPhotonCount() const { return fOpticalPhotonCount; }
    G4double GetTotalEnergyDeposited() const { return fTotalEnergyDeposited; }

private:
    G4int fOpticalPhotonCount;
    G4double fTotalEnergyDeposited;
    G4bool fFirstOpticalPhoton;

    // Manual scintillation function for alpha particles
    void ManuallyTriggerScintillation(const G4Step* step, G4double energyDeposit);
};

#endif
