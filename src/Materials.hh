#pragma once
#include "G4Material.hh"
#include "G4NistManager.hh"
#include "G4MaterialPropertiesTable.hh"
#include "G4OpticalSurface.hh"

// a ideia é :
    // ter uma função que constroi todos os materiais (propriedades, etxc)
    // ter uma função que retorna o material (chama ele quando eu preciso)
    // declarar o material

class Materials {
public:
    Materials();
    ~Materials();
    void PrintNistMaterials();
    void Construct(); // funcao que vai construir os materiais que precisam ser craftados

    // FUNCOES DE PUXAR OS MATERIAIS
    G4Material* GetVikuiti() const {return Vikuiti;} ; 
    G4Material* GetlAr() const {return lAr;} ; 
    G4Material* GetTeflon() const {return Teflon;} ; 
    G4Material* Getssteel() const {return ssteel;} ; 
    G4Material* GetAcrylic() const {return Acrylic;} ; 
    G4Material* GetFR4() const {return FR4;} ; 
    G4Material* GetSi() const {return Si;} ; 


    // FUNCOES DE PUXAR AS SUPERFICIES
    G4OpticalSurface* GetlArVikSurface() const {return lArVikSurface;} ; 
    G4OpticalSurface* GetAcryliclArSurface() const {return AcryliclArSurface;} ; 
    G4OpticalSurface* GetSi_lArSurface() const {return Si_lArSurface;} ; 


private:
    // Declarando materiais
    G4Material* Teflon; 
    G4Material* ssteel; 
    G4Material* Vikuiti; 
    G4Material* lAr; 
    G4Material* Acrylic; 
    G4Material* FR4; 
    G4Material* Si;

    // Declarando Optical Surfaces
    G4OpticalSurface* lArVikSurface;
    G4OpticalSurface* AcryliclArSurface;
    G4OpticalSurface* Si_lArSurface;


};