#include "MyDetector.h"
#include "G4VUserDetectorConstruction.hh"
#include "G4Material.hh"
#include "G4NistManager.hh"
#include "G4Box.hh"
#include "G4SystemOfUnits.hh"
#include "G4LogicalVolume.hh"
#include "G4PVPlacement.hh"
#include "G4SubtractionSolid.hh"
#include "G4Tubs.hh"
#include "G4SDManager.hh"
#include "G4Sphere.hh"
#include "CLHEP/Units/SystemOfUnits.h"
#include "SensitiveDetector.hh"
#include <G4ios.hh>
#include "G4MultiUnion.hh"
#include "G4Color.hh"
#include "G4VisAttributes.hh"
#include "G4GDMLParser.hh"
#include "G4RotationMatrix.hh"
#include "Materials.hh"
#include <iostream>
#include "G4ios.hh"
#include "G4OpticalSurface.hh"
#include "G4LogicalSkinSurface.hh"
#include "G4LogicalBorderSurface.hh"

using namespace CLHEP;
using namespace std;

G4VPhysicalVolume *MyDetector::Construct()
{
    Materials *materials = new Materials();
    materials->Construct();
    materials->PrintNistMaterials();


///////////////////////////////////////////////////////// MATERIAIS IMPORTADOS DA CLASSE MATERIALS.CPP///////////////////////////
    auto vikuiti = materials->GetVikuiti(); 
    auto lAr = materials->GetlAr();
    auto teflon = materials->GetTeflon();
    auto FR4 = materials->GetFR4();
    auto Si = materials->GetSi();
    auto Acrylic = materials->GetAcrylic();
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Rotacoes//////////////////////////////////////
    auto rotZ180 = new G4RotationMatrix();
    rotZ180->rotateZ(180 * deg);
    ///////////////////////////////////////////
   
    //////////////////////////////////////////MUNDO///////////////////////////////////////////
    auto worldBox = new G4Box("World", 15 * cm, 15 * cm, 15 * cm);
    auto logicWorld = new G4LogicalVolume(worldBox, lAr, "LogicalWorld");
    auto physWorld = new G4PVPlacement(0, {0, 0, 0}, logicWorld, "World", 0, false, 0, true);

    ////////////////////////// CAIXA EXTERIOR (TEFLON) ///////////////////////////
    auto caixaTeflon = new G4Box("CaixaTeflon", 5.1 * cm, 5.8 * cm, 5.1 * cm);
    auto logCaixaTeflon = new G4LogicalVolume(caixaTeflon, teflon, "LogCaixaTeflon");
    auto physCaixaTeflon = new G4PVPlacement(0, {0, 0, 0}, logCaixaTeflon, "World/CaixaTeflon", logicWorld, false, 0, true);

    /////////////// CRIANDO CAIXA DE VIKUITI //////////////////////////////////////////////
    auto caixaVikuiti = new G4Box("CaixaVikuiti", 5.0 * cm, 5.7 * cm, 5.0 * cm);
    auto logCaixaVikuiti = new G4LogicalVolume(caixaVikuiti, vikuiti, "LogCaixaVikuiti");
    auto physCaixaVikuiti = new G4PVPlacement(0, {0, 0, 0}, logCaixaVikuiti, "World/CaixaTeflon/CaixaVikuiti", logCaixaTeflon, false, 0, true);

    //////////////////  REGIAO DE ARGONIO ENTRE AS CAIXAS //////////////////////////
    auto caixaArgonio = new G4Box("CaixaExterior", 4.99 * cm, 5.69 * cm, 4.99 * cm);
    auto logCaixa = new G4LogicalVolume(caixaArgonio, lAr , "LogCaixaExterior");
    auto physCaixaArgonio = new G4PVPlacement(0, {0, 0, 0}, logCaixa, "World/CaixaTeflon/CaixaVikuiti/Argonio", logCaixaVikuiti, false, 0, true);

    // Importar GDML do PCB
    G4GDMLParser parser;
    parser.Read("/home/<USER>/geant4/codigos-testes/geo_criostato/data/stls/pcb_FR4.gdml", false);

    // Obter o LogicalVolume do GDML
    auto importedLogical = parser.GetVolume("pcb_FR4");

    // Pegar o sólido do volume importado
    auto solidPCB = importedLogical->GetSolid();

    // Criar novo LogicalVolume com o material FR4
    auto logicPCB = new G4LogicalVolume(solidPCB, vikuiti, "LogicPCB_FR4");

    // Visual
    auto visPCB = new G4VisAttributes(G4Colour::Green());
    visPCB->SetForceSolid(true);
    logicPCB->SetVisAttributes(visPCB);


/* O BLOCO COMENTADO ABAIXO POSICIONA OS PCBS NO MUNDO (OPCIONAL)
    // Posicionar 4 PCBs com material correto
     auto physPCB1 = new G4PVPlacement(nullptr, {0, -1 * cm, 5 * cm - 1.5 * mm}, logicPCB, "World/CaixaTeflon/CaixaVikuiti/Argonio/PCB1", logCaixa, false, 0, true);
    auto physPCB2 = new G4PVPlacement(nullptr, {0, -1 * cm, -5 * cm + 0 * mm}, logicPCB, "World/CaixaTeflon/CaixaVikuiti/Argonio/PCB2", logCaixa, false, 1, true);
    auto physPCB3 = new G4PVPlacement(rotZ180, {0, 1 * cm, 5 * cm - 1.5 * mm}, logicPCB, "World/CaixaTeflon/CaixaVikuiti/Argonio/PCB3", logCaixa, false, 2, true);
    auto physPCB4 = new G4PVPlacement(rotZ180, {0, 1 * cm, -5 * cm + 0 * mm}, logicPCB, "World/CaixaTeflon/CaixaVikuiti/Argonio/PCB4", logCaixa, false, 3, true);

    // AQUI FICA O BLOCO QUE DEFINE E POSICIONA OS SIPMS NA POSIÇÃO DELES COM PCBS

    // SiPMs (multiunion)
    G4double lado = 6.5 * mm;
    G4double esp = 1 * mm;
    G4double espacamento = 2 * lado;
    auto solidSiPM = new G4Box("SiPM", lado / 2, lado / 2, esp / 2);

    auto unionSiPMs = new G4MultiUnion("UnionSiPMs");
    for (int i = 0; i < 4; ++i)
    {
        double x = (i - 1.5) * espacamento;
        unionSiPMs->AddNode(*solidSiPM, G4Transform3D(G4RotationMatrix(), G4ThreeVector(x, 0, 0)));
    }
    unionSiPMs->Voxelize();

    auto visSiPMs = new G4VisAttributes(G4Colour::White());
    visSiPMs->SetForceSolid(true);
    std::vector<G4VPhysicalVolume*> physSiPMs;

    for (int i = 0; i < 4; ++i)
    {
        G4double y = (i < 2) ? lado / 2 : -lado / 2;
        G4double z = (i % 2 == 0) ? 5 * cm - 2 * mm : -5 * cm + 2 * mm;
        auto rot = (i < 2) ? nullptr : rotZ180;

        logicSiPMs = new G4LogicalVolume(unionSiPMs, Si, "SiPMs" + std::to_string(i));
        logicSiPMs->SetVisAttributes(visSiPMs);

        auto phys = new G4PVPlacement(
            rot,
            {0, y, z},
            logicSiPMs,
            "World/CaixaTeflon/CaixaVikuiti/Argonio/SiPMs" + std::to_string(i),
            logCaixa,
            false,
            i,
            true
        );

        physSiPMs.push_back(phys);
        // Definir a interface optica entre SiPM e lAr
        auto Si_lArSurface = materials->GetSi_lArSurface();
        auto Si_lArBorderSurface = new G4LogicalBorderSurface("SiPM -> lAr interface", phys, physCaixaArgonio, Si_lArSurface);
        auto lArSiBorderSurface = new G4LogicalBorderSurface("lAr -> SiPM interface", physCaixaArgonio, phys, Si_lArSurface);

    }
    */

    // SiPMs (multiunion)
    G4double lado = 6.5 * mm;
    G4double esp = 1 * mm;
    G4double espacamento = 2 * lado;
    auto solidSiPM = new G4Box("SiPM", lado / 2, lado / 2, esp / 2);

    auto unionSiPMs = new G4MultiUnion("UnionSiPMs");
    for (int i = 0; i < 4; ++i)
    {
        double x = (i - 1.5) * espacamento;
        unionSiPMs->AddNode(*solidSiPM, G4Transform3D(G4RotationMatrix(), G4ThreeVector(x, 0, 0)));
    }
    unionSiPMs->Voxelize();

    auto visSiPMs = new G4VisAttributes(G4Colour::White());
    visSiPMs->SetForceSolid(true);

    logicSiPMsVector;
    std::vector<G4VPhysicalVolume*> physSiPMs;

    for (int i = 0; i < 4; ++i)
    {
        G4double y = (i < 2) ? lado / 2 : -lado / 2;
        G4double z = ((i % 2 == 0) ? 5 * cm - 0.8 * mm : -5 * cm + 0.8 * mm);
        auto rot = (i < 2) ? nullptr : rotZ180;

        logicSiPMs = new G4LogicalVolume(unionSiPMs, Si, "SiPMs" + std::to_string(i));
        logicSiPMs->SetVisAttributes(visSiPMs);
        logicSiPMsVector.push_back(logicSiPMs);

        auto phys = new G4PVPlacement(
            rot,
            {0, y, z},
            logicSiPMs,
            "World/CaixaTeflon/CaixaVikuiti/Argonio/SiPMs" + std::to_string(i),
            logCaixa,
            false,
            i,
            true
        );

        physSiPMs.push_back(phys);
        // Definir a interface optica entre SiPM e lAr
        auto Si_lArSurface = materials->GetSi_lArSurface();
        auto Si_lArBorderSurface = new G4LogicalBorderSurface("SiPM -> lAr interface", phys, physCaixaArgonio, Si_lArSurface);
        auto lArSiBorderSurface = new G4LogicalBorderSurface("lAr -> SiPM interface", physCaixaArgonio, phys, Si_lArSurface);

    }
    ///////////////////////////// CAIXA INTERNA (ACRILICO + PEN) ///////////////////////////
    // --- Caixa com espessura (PEN ou acrílico)
    auto CaixaAcrilico = new G4Box("CaixaAcrilico", 7.3/2 * cm, 8.6/2 * cm, 7.3/2 * cm);
    auto logCaixaAcrilico = new G4LogicalVolume(CaixaAcrilico, Acrylic, "LogCaixaAcrilico");
    auto physCaixaAcrilico = new G4PVPlacement(0, {0, 0, 0}, logCaixaAcrilico, "World/CaixaTeflon/CaixaVikuiti/Argonio/CaixaAcrilico", logCaixa, false, 0, true);

    //////////////////////////// CAIXA INTERNA DE ARGONIO ///////////////////////////
    auto interiorCaixaAcrilico = new G4Box("InteriorCaixaAcrilico", 7.0/2 * cm, 8.3/2 * cm, 7.0/2 * cm);
    auto logInteriorCaixaAcrilico = new G4LogicalVolume(interiorCaixaAcrilico, lAr, "LogInteriorCaixa");
    auto physInteriorCaixaAcrilico = new G4PVPlacement(0, {0, 0, 0}, logInteriorCaixaAcrilico, "World/CaixaTeflon/CaixaVikuiti/Argonio/CaixaAcrilico/InteriorCaixa", logCaixaAcrilico, false, 0, true);

    // Visual
    logicWorld->SetVisAttributes(G4VisAttributes::GetInvisible());
    logCaixaVikuiti->SetVisAttributes(new G4VisAttributes(G4Colour(0.9, 0.9, 0.9, 0.1)));
    logInteriorCaixaAcrilico->SetVisAttributes(new G4VisAttributes(G4Colour(0.9, 0.3, 0.4, 0.1)));
    logCaixa->SetVisAttributes(new G4VisAttributes(G4Colour(0.9, 0.9, 0.9, 0.1)));
    logCaixaAcrilico->SetVisAttributes(new G4VisAttributes(G4Colour(0.9, 0.3, 0.4, 0.1)));
    logCaixaTeflon->SetVisAttributes(new G4VisAttributes(G4Colour(0.9, 0.9, 0.9, 0.1)));

//////////////////////////// LOGICAL BORDER SURFACES ////////////////////////////////////////

    // lAr e vikuiti
    auto lArVikSurface = materials->GetlArVikSurface(); // pega a optical surface
    auto lArVikBorderSurface = new G4LogicalBorderSurface("lAr -> vikuiti interface", physCaixaArgonio, physCaixaVikuiti, lArVikSurface); // lar - vikuiti
    auto ViklarBorderSurface = new G4LogicalBorderSurface("vikuiti -> lAr interface", physCaixaVikuiti, physCaixaArgonio, lArVikSurface); // vikuiti - lar




/*
    // PCB(vik) e lAr (COMENTAR SE TIVER SEM OS PCBS)
    auto lArPCBSurface = materials->GetlArVikSurface();
    auto lArPCBBorderSurface = new G4LogicalBorderSurface("lAr -> PCB interface", physCaixaArgonio, physPCB1, lArPCBSurface);
    auto lArPCBBorderSurface2 = new G4LogicalBorderSurface("lAr -> PCB interface", physCaixaArgonio, physPCB2, lArPCBSurface);
    auto lArPCBBorderSurface3 = new G4LogicalBorderSurface("lAr -> PCB interface", physCaixaArgonio, physPCB3, lArPCBSurface);
    auto lArPCBBorderSurface4 = new G4LogicalBorderSurface("lAr -> PCB interface", physCaixaArgonio, physPCB4, lArPCBSurface);
    auto PCBlArBorderSurface = new G4LogicalBorderSurface("PCB -> lAr interface", physPCB1, physCaixaArgonio, lArPCBSurface);
    auto PCBlArBorderSurface2 = new G4LogicalBorderSurface("PCB -> lAr interface", physPCB2, physCaixaArgonio, lArPCBSurface);
    auto PCBlArBorderSurface3 = new G4LogicalBorderSurface("PCB -> lAr interface", physPCB3, physCaixaArgonio, lArPCBSurface);
    auto PCBlArBorderSurface4 = new G4LogicalBorderSurface("PCB -> lAr interface", physPCB4, physCaixaArgonio, lArPCBSurface);
*/




    // vikuiti e acrilico
    auto lArAcrylicSurface = materials->GetAcryliclArSurface();
    auto lArAcrylicBorderSurface = new G4LogicalBorderSurface("lAr -> acrylic interface", physInteriorCaixaAcrilico, physCaixaAcrilico, lArAcrylicSurface);
    
    auto AcryliclArSurface = materials->GetAcryliclArSurface();
    auto AcryliclArBorderSurface = new G4LogicalBorderSurface("acrylic -> lAr interface", physCaixaAcrilico, physInteriorCaixaAcrilico, AcryliclArSurface);

    // SiPMs e lAr
    auto Si_lArSurface = materials->GetSi_lArSurface();

    // Posicionamento final do mundo
    return physWorld;
}

/*
void MyDetector::ConstructSDandField() {
    auto sd = new SensitiveDetector("SiPMs");
    for (auto logic : logicSiPMsVector)
        logic->SetSensitiveDetector(sd);
}
*/

// separando os sipms em VUV e visivel
void MyDetector::ConstructSDandField(){
    auto sdVUV = new SensitiveDetector("SiPM VUV");
    auto sdVIS = new SensitiveDetector("SiPM VIS");

    for (size_t i = 0; i < logicSiPMsVector.size(); ++i) {
    if (i < 2) { // ou qualquer condição que defina VUV/Visível
        logicSiPMsVector[i]->SetSensitiveDetector(sdVUV);
    } else {
        logicSiPMsVector[i]->SetSensitiveDetector(sdVIS);
    }
}
}