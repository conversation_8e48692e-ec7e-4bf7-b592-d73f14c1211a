# Definir a versão mínima do CMake e o nome do projeto
cmake_minimum_required(VERSION 3.16...3.27)
project(Tutorials2024)

# Ativar todos os drivers UI e Vis do Geant4 por padrão
option(WITH_GEANT4_UIVIS "Build example with Geant4 UI and Vis drivers" ON)

# Encontrar o pacote Qt5 e os componentes necessários
find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets)
find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets OpenGL)

# Encontrar o pacote Geant4 com todos os componentes necessários
find_package(Geant4 REQUIRED ui_all vis_all analysis)

# Configurar os diretórios de inclusão e definições de compilação do Geant4
include(${Geant4_USE_FILE})

# Definir diretórios de inclusão adicionais
include_directories(
    ${Geant4_INCLUDE_DIR}
    ${PROJECT_SOURCE_DIR}/src
    "/snap/root-framework/current/usr"

)

# Localize os arquivos fontes e cabeçalhos
set(SOURCE_FILES
    main.cpp
    #src/MyDetector.cpp
    #src/SiPM_Box_Detector.cpp
    src/lArscint.cpp
    src/MyPrimaryGenerator.cpp
    src/MyActionInitialization.cpp
    src/Materials.cpp
    src/PhysicsList.cpp
    src/SensitiveDetector.cpp
    src/MyHit.cpp
    src/RunAction.cpp
)

# Adicionar o executável ao projeto
add_executable(tutorial ${SOURCE_FILES})

# Link com as bibliotecas Geant4 e Qt5
#target_link_libraries(tutorial ${Geant4_LIBRARIES} Qt5::Core Qt5::Gui Qt5::Widgets)
target_link_libraries(tutorial ${Geant4_LIBRARIES} Qt5::Core Qt5::Gui Qt5::Widgets Qt5::OpenGL)

# Adicionar o programa aos alvos do projeto
add_custom_target(Tutorials2024 DEPENDS tutorial)

# Instalar o executável no diretório 'bin' dentro do CMAKE_INSTALL_PREFIX
install(TARGETS tutorial DESTINATION ${CMAKE_CURRENT_SOURCE_DIR})

# Encontrar o ROOT
find_package(ROOT REQUIRED)

# Adicionar o ROOT ao projeto
include_directories(${ROOT_INCLUDE_DIRS})
target_link_libraries(tutorial ${ROOT_LIBRARIES})