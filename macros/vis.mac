# Abre o visualizador OpenGL (pode trocar por OGLI ou RayTracer se preferir)
/vis/open OGLS

# Cria uma nova cena e adiciona todos os volumes
/vis/scene/create
/vis/scene/add/volume
/vis/sceneHandler/attach

# Estilo de visualização (surface = sólido com bordas)
/vis/viewer/set/style surface

# Adiciona eixos de referência na origem para orientação
/vis/scene/add/axes 0 0 0 10 cm

# Melhora renderizaçasasão de curvas e círculos
/vis/viewer/set/lineSegmentsPerCircle 100

# Centraliza e define o ponto de vista (Theta=90, Phi=0)
/vis/viewer/set/viewpointThetaPhi 45 45

# Permite atualizar a visualização automaticamente
/vis/viewer/set/autoRefresh true
/vis/viewer/flush
/vis/scene/add/trajectories smooth
/vis/scene/add/hits
/vis/scene/endOfEventAction accumulate

# Configuração do gerador de partículas
/gun/particle alpha
/gun/energy 4.5 MeV
#/gun/energy 7.5 eV


/gun/position 0 0 0.1 cm
#/gun/polarization 1 1 1
#/random/setSeeds 0 0
/tracking/verbose 1
/run/beamOn 1
