# Test scintillation macro
/run/verbose 1
/event/verbose 1
/tracking/verbose 1

# Set up visualization
/vis/open OGLS
/vis/scene/create
/vis/scene/add/volume
/vis/sceneHandler/attach
/vis/viewer/set/style surface
/vis/scene/add/axes 0 0 0 10 cm
/vis/viewer/set/viewpointThetaPhi 45 45
/vis/viewer/set/autoRefresh true
/vis/viewer/flush
/vis/scene/add/trajectories smooth
/vis/scene/add/hits
/vis/scene/endOfEventAction accumulate

# Configure particle gun
/gun/particle alpha
/gun/energy 4.5 MeV
/gun/position 0 0 1 mm

# Run a single event
/run/beamOn 1
